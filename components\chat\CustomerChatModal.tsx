/**
 * ## Customer Chat Modal - Integrated Chat Interface
 * Smooth animated chat interface with profile integration
 * Modern chat app experience with slide animations
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Send,
  X,
  Minimize2,
  Maximize2,
  Headphones,
  Clock,
  CheckCircle2,
  Circle,
  ArrowLeft,
  User,
  Phone,
  Mail,
  Settings,
  Star,
  Shield
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface CustomerChatModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName?: string
  userEmail?: string
  // Position and size control
  position?: 'bottom-right' | 'bottom-left' | 'center'
  isMinimized?: boolean
  onToggleMinimize?: () => void
}

export function CustomerChatModal({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
  position = 'bottom-right',
  isMinimized = false,
  onToggleMinimize
}: CustomerChatModalProps) {
  const [messageInput, setMessageInput] = useState('')
  const [currentView, setCurrentView] = useState<'main' | 'chat' | 'profile'>('main')
  const [selectedAgent, setSelectedAgent] = useState<any>(null)
  const [isAnimating, setIsAnimating] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Mock support agents
  const supportAgents = [
    {
      id: 'agent-1',
      name: 'أحمد محمد',
      role: 'مختص الدعم الفني',
      avatar: 'أ',
      isOnline: true,
      responseTime: '< دقيقة واحدة',
      rating: 4.9,
      specialties: ['شحن الألعاب', 'المدفوعات', 'المشاكل التقنية']
    },
    {
      id: 'agent-2',
      name: 'فاطمة علي',
      role: 'مختصة خدمة العملاء',
      avatar: 'ف',
      isOnline: true,
      responseTime: '< 2 دقيقة',
      rating: 4.8,
      specialties: ['الطلبات', 'الاستفسارات العامة', 'المساعدة']
    },
    {
      id: 'agent-3',
      name: 'محمد سالم',
      role: 'مدير الدعم',
      avatar: 'م',
      isOnline: false,
      responseTime: '< 5 دقائق',
      rating: 5.0,
      specialties: ['المشاكل المعقدة', 'الشكاوى', 'الاقتراحات']
    }
  ]

  // Chat hook
  const {
    messages,
    isLoadingMessages,
    sendMessage,
    markAsRead,
    isOnline,
    typingUsers,
    error
  } = useChat({
    userId,
    userType: 'customer'
  })

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Auto-mark messages as read when modal is open
  useEffect(() => {
    if (isOpen && !isMinimized) {
      const unreadMessages = messages
        .filter(msg => !msg.isRead && msg.senderType === 'admin')
        .map(msg => msg.id)
      if (unreadMessages.length > 0) {
        markAsRead(unreadMessages)
      }
    }
  }, [isOpen, isMinimized, messages, markAsRead])

  // Handle message sending
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return
    const message = messageInput.trim()
    setMessageInput('')
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Smooth view transitions
  const transitionToView = (newView: 'main' | 'chat' | 'profile', agent?: any) => {
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentView(newView)
      if (agent) setSelectedAgent(agent)
      setIsAnimating(false)
    }, 150)
  }

  // Handle agent selection
  const handleAgentSelect = (agent: any) => {
    transitionToView('chat', agent)
  }

  // Handle back navigation
  const handleBack = () => {
    if (currentView === 'chat') {
      transitionToView('main')
    } else if (currentView === 'profile') {
      transitionToView('chat')
    }
  }

  // Handle profile view
  const handleViewProfile = () => {
    transitionToView('profile')
  }

  if (!isOpen) return null

  // Position classes for modal
  const getModalClasses = () => {
    if (isMinimized) {
      return position === 'bottom-left' 
        ? 'bottom-6 left-6 w-80 h-12'
        : 'bottom-6 right-6 w-80 h-12'
    }
    
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6 w-96 h-[500px]'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-[500px]'
      default:
        return 'bottom-6 right-6 w-96 h-[500px]'
    }
  }

  // Message bubble component
  function MessageBubble({ message }: { message: ChatMessage }) {
    const isFromAdmin = message.senderType === 'admin'

    return (
      <div className={`flex ${isFromAdmin ? 'justify-start' : 'justify-end'} mb-3`}>
        <div className={`
          max-w-[80%] px-3 py-2 rounded-lg text-sm
          ${isFromAdmin 
            ? 'bg-slate-700 text-white rounded-bl-sm' 
            : 'bg-blue-500 text-white rounded-br-sm'
          }
          animate-in slide-in-from-bottom-2 duration-200
        `}>
          <p className="leading-relaxed">{message.message}</p>
          <div className={`
            flex items-center gap-1 mt-1 text-xs
            ${isFromAdmin ? 'text-slate-400' : 'text-blue-100'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            {!isFromAdmin && (
              <div className="flex items-center">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Backdrop */}
      {position === 'center' && !isMinimized && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
        />
      )}

      {/* Modal */}
      <div className={`fixed ${getModalClasses()} z-50 bg-slate-800 rounded-lg border border-slate-700 shadow-xl overflow-hidden transition-all duration-300`}>
        {/* Dynamic Header */}
        <div className="flex items-center justify-between p-3 border-b border-slate-700 bg-slate-800/90">
          <div className="flex items-center gap-2">
            {/* Back Button */}
            {currentView !== 'main' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="p-1 hover:bg-slate-700 mr-1"
              >
                <ArrowLeft className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {/* Header Content */}
            {currentView === 'main' && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <Headphones className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">الدعم الفني</h3>
                  <p className="text-xs text-slate-400">اختر مختص للمساعدة</p>
                </div>
              </>
            )}

            {currentView === 'chat' && selectedAgent && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {selectedAgent.avatar}
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">{selectedAgent.name}</h3>
                  <p className="text-xs text-slate-400">
                    {selectedAgent.isOnline ? (
                      <span className="text-green-400">متاح الآن</span>
                    ) : (
                      'غير متاح حالياً'
                    )}
                  </p>
                </div>
              </>
            )}

            {currentView === 'profile' && selectedAgent && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">الملف الشخصي</h3>
                  <p className="text-xs text-slate-400">{selectedAgent.name}</p>
                </div>
              </>
            )}
          </div>

          <div className="flex items-center gap-1">
            {/* Profile Button (only in chat view) */}
            {currentView === 'chat' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewProfile}
                className="p-1 hover:bg-slate-700"
              >
                <User className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {onToggleMinimize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="p-1 hover:bg-slate-700"
              >
                {isMinimized ? (
                  <Maximize2 className="h-4 w-4 text-slate-400" />
                ) : (
                  <Minimize2 className="h-4 w-4 text-slate-400" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1 hover:bg-slate-700"
            >
              <X className="h-4 w-4 text-slate-400" />
            </Button>
          </div>
        </div>

        {/* Content */}
        {!isMinimized && (
          <>
            {/* Messages */}
            <ScrollArea className="flex-1 p-3 h-80">
              {error && (
                <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-2 rounded mb-3 text-sm">
                  {error}
                </div>
              )}

              {isLoadingMessages ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                </div>
              ) : messages.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <MessageSquare className="h-8 w-8 text-slate-500 mx-auto mb-2" />
                    <p className="text-slate-400 text-sm">مرحباً! كيف يمكننا مساعدتك؟</p>
                  </div>
                </div>
              ) : (
                <>
                  {messages.map((message) => (
                    <MessageBubble key={message.id} message={message} />
                  ))}
                  <div ref={messagesEndRef} />
                </>
              )}

              {/* Typing indicator */}
              {typingUsers.length > 0 && (
                <div className="flex justify-start mb-3">
                  <div className="bg-slate-700 px-3 py-2 rounded-lg text-sm text-slate-300">
                    <div className="flex items-center gap-1">
                      <div className="flex gap-1">
                        <div className="w-1 h-1 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                        <div className="w-1 h-1 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                        <div className="w-1 h-1 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                      </div>
                      <span className="text-xs">الدعم يكتب...</span>
                    </div>
                  </div>
                </div>
              )}
            </ScrollArea>

            {/* Input */}
            <div className="border-t border-slate-700 p-3">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="اكتب رسالتك..."
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 text-sm"
                  maxLength={1000}
                  disabled={!isOnline}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim() || !isOnline}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-3"
                  size="sm"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  )
}
