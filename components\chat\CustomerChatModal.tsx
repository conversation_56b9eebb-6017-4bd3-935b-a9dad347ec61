/**
 * ## Customer Chat Modal - Integrated Chat Interface
 * Smooth animated chat interface with profile integration
 * Modern chat app experience with slide animations
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Send,
  X,
  Minimize2,
  Maximize2,
  Headphones,
  Clock,
  CheckCircle2,
  Circle,
  ArrowLeft,
  User,
  Phone,
  Mail,
  Settings,
  Star,
  Shield
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface CustomerChatModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName?: string
  userEmail?: string
  // Position and size control
  position?: 'bottom-right' | 'bottom-left' | 'center'
  isMinimized?: boolean
  onToggleMinimize?: () => void
}

export function CustomerChatModal({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
  position = 'bottom-right',
  isMinimized = false,
  onToggleMinimize
}: CustomerChatModalProps) {
  const [messageInput, setMessageInput] = useState('')
  const [currentView, setCurrentView] = useState<'main' | 'chat' | 'profile'>('main')
  const [selectedAgent, setSelectedAgent] = useState<any>(null)
  const [isAnimating, setIsAnimating] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Mock support agents
  const supportAgents = [
    {
      id: 'agent-1',
      name: 'أحمد محمد',
      role: 'مختص الدعم الفني',
      avatar: 'أ',
      isOnline: true,
      responseTime: '< دقيقة واحدة',
      rating: 4.9,
      specialties: ['شحن الألعاب', 'المدفوعات', 'المشاكل التقنية']
    },
    {
      id: 'agent-2',
      name: 'فاطمة علي',
      role: 'مختصة خدمة العملاء',
      avatar: 'ف',
      isOnline: true,
      responseTime: '< 2 دقيقة',
      rating: 4.8,
      specialties: ['الطلبات', 'الاستفسارات العامة', 'المساعدة']
    },
    {
      id: 'agent-3',
      name: 'محمد سالم',
      role: 'مدير الدعم',
      avatar: 'م',
      isOnline: false,
      responseTime: '< 5 دقائق',
      rating: 5.0,
      specialties: ['المشاكل المعقدة', 'الشكاوى', 'الاقتراحات']
    }
  ]

  // Chat hook
  const {
    messages,
    isLoadingMessages,
    sendMessage,
    markAsRead,
    isOnline,
    typingUsers,
    error
  } = useChat({
    userId,
    userType: 'customer'
  })

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Auto-mark messages as read when modal is open
  useEffect(() => {
    if (isOpen && !isMinimized) {
      const unreadMessages = messages
        .filter(msg => !msg.isRead && msg.senderType === 'admin')
        .map(msg => msg.id)
      if (unreadMessages.length > 0) {
        markAsRead(unreadMessages)
      }
    }
  }, [isOpen, isMinimized, messages, markAsRead])

  // Handle message sending
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return
    const message = messageInput.trim()
    setMessageInput('')
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Smooth view transitions
  const transitionToView = (newView: 'main' | 'chat' | 'profile', agent?: any) => {
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentView(newView)
      if (agent) setSelectedAgent(agent)
      setIsAnimating(false)
    }, 150)
  }

  // Handle agent selection
  const handleAgentSelect = (agent: any) => {
    transitionToView('chat', agent)
  }

  // Handle back navigation
  const handleBack = () => {
    if (currentView === 'chat') {
      transitionToView('main')
    } else if (currentView === 'profile') {
      transitionToView('chat')
    }
  }

  // Handle profile view
  const handleViewProfile = () => {
    transitionToView('profile')
  }

  if (!isOpen) return null

  // Position classes for modal - Enhanced with better sizing
  const getModalClasses = () => {
    if (isMinimized) {
      return position === 'bottom-left'
        ? 'bottom-4 left-4 w-80 h-14'
        : 'bottom-4 right-4 w-80 h-14'
    }

    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4 w-[400px] h-[600px] max-h-[90vh]'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[600px] max-h-[90vh]'
      default:
        return 'bottom-4 right-4 w-[400px] h-[600px] max-h-[90vh]'
    }
  }

  // Message bubble component
  function MessageBubble({ message }: { message: ChatMessage }) {
    const isFromAdmin = message.senderType === 'admin'

    return (
      <div className={`flex ${isFromAdmin ? 'justify-start' : 'justify-end'} mb-3`}>
        <div className={`
          max-w-[80%] px-3 py-2 rounded-lg text-sm
          ${isFromAdmin 
            ? 'bg-slate-700 text-white rounded-bl-sm' 
            : 'bg-blue-500 text-white rounded-br-sm'
          }
          animate-in slide-in-from-bottom-2 duration-200
        `}>
          <p className="leading-relaxed">{message.message}</p>
          <div className={`
            flex items-center gap-1 mt-1 text-xs
            ${isFromAdmin ? 'text-slate-400' : 'text-blue-100'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            {!isFromAdmin && (
              <div className="flex items-center">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Backdrop */}
      {position === 'center' && !isMinimized && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
        />
      )}

      {/* Enhanced Modal with Better Styling */}
      <div className={`
        fixed ${getModalClasses()} z-50
        bg-gradient-to-b from-slate-800 to-slate-900
        rounded-2xl border border-slate-600/50
        shadow-2xl shadow-black/25
        backdrop-blur-sm
        overflow-hidden transition-all duration-300
        ${isMinimized ? 'shadow-lg' : 'shadow-2xl'}
        flex flex-col
      `}>
        {/* Enhanced Dynamic Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-600/30 bg-gradient-to-r from-slate-800/95 to-slate-700/95 backdrop-blur-sm">
          <div className="flex items-center gap-2">
            {/* Back Button */}
            {currentView !== 'main' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="p-1 hover:bg-slate-700 mr-1"
              >
                <ArrowLeft className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {/* Header Content */}
            {currentView === 'main' && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <Headphones className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">الدعم الفني</h3>
                  <p className="text-xs text-slate-400">اختر مختص للمساعدة</p>
                </div>
              </>
            )}

            {currentView === 'chat' && selectedAgent && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {selectedAgent.avatar}
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">{selectedAgent.name}</h3>
                  <p className="text-xs text-slate-400">
                    {selectedAgent.isOnline ? (
                      <span className="text-green-400">متاح الآن</span>
                    ) : (
                      'غير متاح حالياً'
                    )}
                  </p>
                </div>
              </>
            )}

            {currentView === 'profile' && selectedAgent && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">الملف الشخصي</h3>
                  <p className="text-xs text-slate-400">{selectedAgent.name}</p>
                </div>
              </>
            )}
          </div>

          <div className="flex items-center gap-1">
            {/* Profile Button (only in chat view) */}
            {currentView === 'chat' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewProfile}
                className="p-1 hover:bg-slate-700"
              >
                <User className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {onToggleMinimize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="p-1 hover:bg-slate-700"
              >
                {isMinimized ? (
                  <Maximize2 className="h-4 w-4 text-slate-400" />
                ) : (
                  <Minimize2 className="h-4 w-4 text-slate-400" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1 hover:bg-slate-700"
            >
              <X className="h-4 w-4 text-slate-400" />
            </Button>
          </div>
        </div>

        {/* Enhanced Animated Content Container - Fixed Height Issue */}
        {!isMinimized && (
          <div className="relative flex-1 overflow-hidden bg-slate-900/20">
            {/* Main Agent Selection View */}
            <div className={`
              absolute inset-0 transition-transform duration-300 ease-in-out
              ${currentView === 'main' ? 'translate-x-0' : '-translate-x-full'}
              ${isAnimating ? 'opacity-50' : 'opacity-100'}
            `}>
              <AgentSelectionView
                agents={supportAgents}
                onAgentSelect={handleAgentSelect}
                isAnimating={isAnimating}
              />
            </div>

            {/* Chat Conversation View */}
            <div className={`
              absolute inset-0 transition-transform duration-300 ease-in-out
              ${currentView === 'chat' ? 'translate-x-0' :
                currentView === 'main' ? 'translate-x-full' : '-translate-x-full'}
              ${isAnimating ? 'opacity-50' : 'opacity-100'}
            `}>
              <ChatConversationView
                messages={messages}
                messageInput={messageInput}
                setMessageInput={setMessageInput}
                handleSendMessage={handleSendMessage}
                handleKeyPress={handleKeyPress}
                messagesEndRef={messagesEndRef}
                inputRef={inputRef}
                isLoadingMessages={isLoadingMessages}
                typingUsers={typingUsers}
                error={error}
                isOnline={isOnline}
                selectedAgent={selectedAgent}
                isAnimating={isAnimating}
              />
            </div>

            {/* Profile View */}
            <div className={`
              absolute inset-0 transition-transform duration-300 ease-in-out
              ${currentView === 'profile' ? 'translate-x-0' : 'translate-x-full'}
              ${isAnimating ? 'opacity-50' : 'opacity-100'}
            `}>
              <ProfileView
                agent={selectedAgent}
                isAnimating={isAnimating}
              />
            </div>
          </div>
        )}
      </div>
    </>
  )
}

// Enhanced Agent Selection View Component
function AgentSelectionView({
  agents,
  onAgentSelect,
  isAnimating
}: {
  agents: any[],
  onAgentSelect: (agent: any) => void,
  isAnimating: boolean
}) {
  return (
    <div className="h-full flex flex-col">
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {/* Enhanced Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
              <Headphones className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">اختر مختص الدعم</h3>
            <p className="text-slate-400 text-sm leading-relaxed">اختر المختص المناسب لمساعدتك من فريق الدعم المتخصص</p>
          </div>

          {/* Enhanced Agent Cards */}
          {agents.map((agent) => (
            <div
              key={agent.id}
              onClick={() => !isAnimating && onAgentSelect(agent)}
              className={`
                group p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/30
                hover:from-slate-700/60 hover:to-slate-600/40
                rounded-xl cursor-pointer transition-all duration-300
                border border-slate-600/20 hover:border-slate-500/40
                shadow-sm hover:shadow-lg hover:shadow-blue-500/10
                ${isAnimating ? 'pointer-events-none opacity-50' : 'hover:scale-[1.02] hover:-translate-y-1'}
                backdrop-blur-sm
              `}
            >
              <div className="flex items-center gap-4">
                {/* Enhanced Agent Avatar */}
                <div className="relative">
                  <div className={`
                    w-14 h-14 rounded-2xl flex items-center justify-center text-white font-bold text-lg
                    shadow-lg transition-all duration-300 group-hover:scale-110
                    ${agent.isOnline
                      ? 'bg-gradient-to-br from-green-500 to-green-600 shadow-green-500/25'
                      : 'bg-gradient-to-br from-slate-500 to-slate-600 shadow-slate-500/25'
                    }
                  `}>
                    {agent.avatar}
                  </div>
                  {agent.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-3 border-slate-800 animate-pulse" />
                  )}
                </div>

                {/* Enhanced Agent Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-bold text-white truncate text-lg group-hover:text-blue-300 transition-colors">
                      {agent.name}
                    </h4>
                    <div className="flex items-center gap-1 bg-yellow-500/20 px-2 py-1 rounded-lg">
                      <Star className="h-3 w-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-yellow-400 font-semibold">{agent.rating}</span>
                    </div>
                  </div>
                  <p className="text-slate-300 text-sm mb-2 font-medium">{agent.role}</p>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="flex items-center gap-1 text-slate-400">
                      <Clock className="h-3 w-3" />
                      <span>متوسط الرد: {agent.responseTime}</span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Status Badge */}
                <div className="text-right">
                  <Badge
                    className={`text-xs font-semibold px-3 py-1 rounded-full transition-all duration-300 ${
                      agent.isOnline
                        ? 'bg-gradient-to-r from-green-500/20 to-green-400/20 text-green-400 border border-green-500/30 shadow-sm shadow-green-500/20'
                        : 'bg-gradient-to-r from-slate-500/20 to-slate-400/20 text-slate-400 border border-slate-500/30'
                    }`}
                  >
                    {agent.isOnline ? 'متاح الآن' : 'مشغول'}
                  </Badge>
                </div>
              </div>

              {/* Enhanced Specialties */}
              <div className="mt-3 flex flex-wrap gap-2">
                {agent.specialties.slice(0, 2).map((specialty: string, index: number) => (
                  <span
                    key={index}
                    className="text-xs bg-gradient-to-r from-blue-500/20 to-blue-400/20 text-blue-300 px-3 py-1.5 rounded-full border border-blue-500/20 font-medium"
                  >
                    {specialty}
                  </span>
                ))}
                {agent.specialties.length > 2 && (
                  <span className="text-xs text-slate-400 bg-slate-700/30 px-2 py-1 rounded-full">
                    +{agent.specialties.length - 2} المزيد
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

// Chat Conversation View Component
function ChatConversationView({
  messages,
  messageInput,
  setMessageInput,
  handleSendMessage,
  handleKeyPress,
  messagesEndRef,
  inputRef,
  isLoadingMessages,
  typingUsers,
  error,
  isOnline,
  selectedAgent,
  isAnimating
}: {
  messages: ChatMessage[],
  messageInput: string,
  setMessageInput: (value: string) => void,
  handleSendMessage: () => void,
  handleKeyPress: (e: React.KeyboardEvent) => void,
  messagesEndRef: React.RefObject<HTMLDivElement>,
  inputRef: React.RefObject<HTMLInputElement>,
  isLoadingMessages: boolean,
  typingUsers: string[],
  error: string | null,
  isOnline: boolean,
  selectedAgent: any,
  isAnimating: boolean
}) {
  // Enhanced Message bubble component
  function MessageBubble({ message }: { message: ChatMessage }) {
    const isFromAgent = message.senderType === 'admin'

    return (
      <div className={`flex ${isFromAgent ? 'justify-start' : 'justify-end'} mb-4`}>
        <div className={`
          max-w-[85%] px-4 py-3 text-sm shadow-lg
          ${isFromAgent
            ? 'bg-gradient-to-br from-slate-700 to-slate-800 text-white rounded-2xl rounded-bl-md border border-slate-600/30'
            : 'bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl rounded-br-md shadow-blue-500/25'
          }
          animate-in slide-in-from-bottom-2 duration-300
          backdrop-blur-sm
        `}>
          <p className="leading-relaxed font-medium">{message.message}</p>
          <div className={`
            flex items-center gap-1 mt-2 text-xs
            ${isFromAgent ? 'text-slate-400' : 'text-blue-100'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            {!isFromAgent && (
              <div className="flex items-center ml-1">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-slate-900/10 to-slate-900/30">
      {/* Enhanced Messages Area */}
      <ScrollArea className="flex-1 p-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-2 rounded mb-3 text-sm">
            {error}
          </div>
        )}

        {isLoadingMessages ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-sm">
              <div className={`
                w-20 h-20 rounded-3xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4
                shadow-2xl transition-all duration-300 hover:scale-105
                ${selectedAgent?.isOnline
                  ? 'bg-gradient-to-br from-green-500 to-green-600 shadow-green-500/30'
                  : 'bg-gradient-to-br from-slate-500 to-slate-600 shadow-slate-500/30'
                }
              `}>
                {selectedAgent?.avatar}
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{selectedAgent?.name}</h4>
              <p className="text-slate-300 text-sm mb-4 font-medium">{selectedAgent?.role}</p>
              <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 p-4 rounded-2xl border border-slate-600/30">
                <p className="text-slate-300 text-sm leading-relaxed">
                  مرحباً! أنا {selectedAgent?.name}، مختص في {selectedAgent?.specialties?.[0]}.
                  كيف يمكنني مساعدتك اليوم؟
                </p>
              </div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageBubble key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}

        {/* Enhanced Typing indicator */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start mb-4">
            <div className="bg-gradient-to-r from-slate-700 to-slate-800 px-4 py-3 rounded-2xl rounded-bl-md text-sm text-slate-300 shadow-lg border border-slate-600/30">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-xs font-medium">{selectedAgent?.name} يكتب...</span>
              </div>
            </div>
          </div>
        )}
      </ScrollArea>

      {/* Enhanced Input Area */}
      <div className="border-t border-slate-600/30 p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-sm">
        <div className="flex gap-3">
          <Input
            ref={inputRef}
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={`اكتب رسالة إلى ${selectedAgent?.name}...`}
            className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 text-sm rounded-xl px-4 py-3 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
            maxLength={1000}
            disabled={!isOnline || isAnimating}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || !isOnline || isAnimating}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-3 rounded-xl shadow-lg hover:shadow-blue-500/25 transition-all duration-200 hover:scale-105"
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Enhanced Profile View Component
function ProfileView({
  agent,
  isAnimating
}: {
  agent: any,
  isAnimating: boolean
}) {
  if (!agent) return null

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-slate-900/10 to-slate-900/30">
      <ScrollArea className="flex-1 p-5">
        <div className="space-y-6">
          {/* Enhanced Profile Header */}
          <div className="text-center">
            <div className={`
              w-24 h-24 rounded-3xl flex items-center justify-center text-white font-bold text-3xl mx-auto mb-4
              shadow-2xl transition-all duration-300 hover:scale-105
              ${agent.isOnline
                ? 'bg-gradient-to-br from-green-500 to-green-600 shadow-green-500/30'
                : 'bg-gradient-to-br from-slate-500 to-slate-600 shadow-slate-500/30'
              }
            `}>
              {agent.avatar}
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">{agent.name}</h3>
            <p className="text-slate-300 mb-3 font-medium">{agent.role}</p>
            <div className="flex items-center justify-center gap-2 bg-slate-800/50 px-4 py-2 rounded-full border border-slate-600/30">
              <div className={`w-3 h-3 rounded-full ${agent.isOnline ? 'bg-green-400 animate-pulse' : 'bg-slate-500'}`} />
              <span className={`text-sm font-medium ${agent.isOnline ? 'text-green-400' : 'text-slate-400'}`}>
                {agent.isOnline ? 'متاح الآن' : 'غير متاح حالياً'}
              </span>
            </div>
          </div>

          {/* Enhanced Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 p-4 rounded-2xl text-center border border-yellow-500/20 shadow-lg">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Star className="h-5 w-5 text-yellow-400 fill-current" />
                <span className="text-2xl font-bold text-white">{agent.rating}</span>
              </div>
              <p className="text-sm text-yellow-300 font-medium">التقييم</p>
            </div>
            <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 p-4 rounded-2xl text-center border border-blue-500/20 shadow-lg">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Clock className="h-5 w-5 text-blue-400" />
                <span className="text-lg font-bold text-white">{agent.responseTime}</span>
              </div>
              <p className="text-sm text-blue-300 font-medium">متوسط الرد</p>
            </div>
          </div>

          {/* Enhanced Specialties */}
          <div>
            <h4 className="text-white font-bold mb-4 flex items-center gap-2 text-lg">
              <Shield className="h-5 w-5 text-blue-400" />
              التخصصات
            </h4>
            <div className="space-y-3">
              {agent.specialties.map((specialty: string, index: number) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-gradient-to-r from-slate-800/50 to-slate-700/30 rounded-xl border border-slate-600/30 hover:border-blue-500/30 transition-all duration-200">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-sm" />
                  <span className="text-slate-200 text-sm font-medium">{specialty}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Contact Info */}
          <div>
            <h4 className="text-white font-bold mb-4 flex items-center gap-2 text-lg">
              <Mail className="h-5 w-5 text-green-400" />
              معلومات الاتصال
            </h4>
            <div className="space-y-3">
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl border border-green-500/20">
                <div className="w-10 h-10 bg-green-500/20 rounded-xl flex items-center justify-center">
                  <Mail className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="text-green-300 text-sm font-medium">البريد الإلكتروني</p>
                  <p className="text-slate-300 text-xs">{agent.name.toLowerCase().replace(' ', '.')}@alraya-store.com</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-xl border border-blue-500/20">
                <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <Phone className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-blue-300 text-sm font-medium">الهاتف المباشر</p>
                  <p className="text-slate-300 text-xs">+966 50 123 {Math.floor(Math.random() * 9000) + 1000}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Working Hours */}
          <div>
            <h4 className="text-white font-bold mb-4 flex items-center gap-2 text-lg">
              <Clock className="h-5 w-5 text-purple-400" />
              ساعات العمل
            </h4>
            <div className="bg-gradient-to-r from-purple-500/10 to-purple-600/10 p-4 rounded-xl border border-purple-500/20">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-purple-300 font-medium">السبت - الخميس</span>
                  <span className="text-slate-300 bg-slate-700/50 px-3 py-1 rounded-lg">9:00 ص - 11:00 م</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-300 font-medium">الجمعة</span>
                  <span className="text-slate-300 bg-slate-700/50 px-3 py-1 rounded-lg">2:00 م - 11:00 م</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="space-y-3">
            <Button
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 rounded-xl shadow-lg hover:shadow-blue-500/25 transition-all duration-200 hover:scale-105"
              disabled={isAnimating}
            >
              <MessageSquare className="h-5 w-5 mr-2" />
              بدء محادثة جديدة
            </Button>
            <Button
              variant="outline"
              className="w-full border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-700/50 py-3 rounded-xl transition-all duration-200 hover:scale-105"
              disabled={isAnimating}
            >
              <Star className="h-5 w-5 mr-2" />
              تقييم المختص
            </Button>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
