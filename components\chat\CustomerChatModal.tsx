/**
 * ## Customer Chat Modal - Integrated Chat Interface
 * Smooth animated chat interface with profile integration
 * Modern chat app experience with slide animations
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Send,
  X,
  Minimize2,
  Maximize2,
  Headphones,
  Clock,
  CheckCircle2,
  Circle,
  ArrowLeft,
  User,
  Phone,
  Mail,
  Settings,
  Star,
  Shield
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface CustomerChatModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName?: string
  userEmail?: string
  // Position and size control
  position?: 'bottom-right' | 'bottom-left' | 'center'
  isMinimized?: boolean
  onToggleMinimize?: () => void
}

export function CustomerChatModal({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
  position = 'bottom-right',
  isMinimized = false,
  onToggleMinimize
}: CustomerChatModalProps) {
  const [messageInput, setMessageInput] = useState('')
  const [currentView, setCurrentView] = useState<'main' | 'chat' | 'profile'>('main')
  const [selectedAgent, setSelectedAgent] = useState<any>(null)
  const [isAnimating, setIsAnimating] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Mock support agents
  const supportAgents = [
    {
      id: 'agent-1',
      name: 'أحمد محمد',
      role: 'مختص الدعم الفني',
      avatar: 'أ',
      isOnline: true,
      responseTime: '< دقيقة واحدة',
      rating: 4.9,
      specialties: ['شحن الألعاب', 'المدفوعات', 'المشاكل التقنية']
    },
    {
      id: 'agent-2',
      name: 'فاطمة علي',
      role: 'مختصة خدمة العملاء',
      avatar: 'ف',
      isOnline: true,
      responseTime: '< 2 دقيقة',
      rating: 4.8,
      specialties: ['الطلبات', 'الاستفسارات العامة', 'المساعدة']
    },
    {
      id: 'agent-3',
      name: 'محمد سالم',
      role: 'مدير الدعم',
      avatar: 'م',
      isOnline: false,
      responseTime: '< 5 دقائق',
      rating: 5.0,
      specialties: ['المشاكل المعقدة', 'الشكاوى', 'الاقتراحات']
    }
  ]

  // Chat hook
  const {
    messages,
    isLoadingMessages,
    sendMessage,
    markAsRead,
    isOnline,
    typingUsers,
    error
  } = useChat({
    userId,
    userType: 'customer'
  })

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Auto-mark messages as read when modal is open
  useEffect(() => {
    if (isOpen && !isMinimized) {
      const unreadMessages = messages
        .filter(msg => !msg.isRead && msg.senderType === 'admin')
        .map(msg => msg.id)
      if (unreadMessages.length > 0) {
        markAsRead(unreadMessages)
      }
    }
  }, [isOpen, isMinimized, messages, markAsRead])

  // Handle message sending
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return
    const message = messageInput.trim()
    setMessageInput('')
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Smooth view transitions
  const transitionToView = (newView: 'main' | 'chat' | 'profile', agent?: any) => {
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentView(newView)
      if (agent) setSelectedAgent(agent)
      setIsAnimating(false)
    }, 150)
  }

  // Handle agent selection
  const handleAgentSelect = (agent: any) => {
    transitionToView('chat', agent)
  }

  // Handle back navigation
  const handleBack = () => {
    if (currentView === 'chat') {
      transitionToView('main')
    } else if (currentView === 'profile') {
      transitionToView('chat')
    }
  }

  // Handle profile view
  const handleViewProfile = () => {
    transitionToView('profile')
  }

  if (!isOpen) return null

  // Position classes for modal
  const getModalClasses = () => {
    if (isMinimized) {
      return position === 'bottom-left' 
        ? 'bottom-6 left-6 w-80 h-12'
        : 'bottom-6 right-6 w-80 h-12'
    }
    
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6 w-96 h-[500px]'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-[500px]'
      default:
        return 'bottom-6 right-6 w-96 h-[500px]'
    }
  }

  // Message bubble component
  function MessageBubble({ message }: { message: ChatMessage }) {
    const isFromAdmin = message.senderType === 'admin'

    return (
      <div className={`flex ${isFromAdmin ? 'justify-start' : 'justify-end'} mb-3`}>
        <div className={`
          max-w-[80%] px-3 py-2 rounded-lg text-sm
          ${isFromAdmin 
            ? 'bg-slate-700 text-white rounded-bl-sm' 
            : 'bg-blue-500 text-white rounded-br-sm'
          }
          animate-in slide-in-from-bottom-2 duration-200
        `}>
          <p className="leading-relaxed">{message.message}</p>
          <div className={`
            flex items-center gap-1 mt-1 text-xs
            ${isFromAdmin ? 'text-slate-400' : 'text-blue-100'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            {!isFromAdmin && (
              <div className="flex items-center">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Backdrop */}
      {position === 'center' && !isMinimized && (
        <div 
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
        />
      )}

      {/* Modal */}
      <div className={`fixed ${getModalClasses()} z-50 bg-slate-800 rounded-lg border border-slate-700 shadow-xl overflow-hidden transition-all duration-300`}>
        {/* Dynamic Header */}
        <div className="flex items-center justify-between p-3 border-b border-slate-700 bg-slate-800/90">
          <div className="flex items-center gap-2">
            {/* Back Button */}
            {currentView !== 'main' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="p-1 hover:bg-slate-700 mr-1"
              >
                <ArrowLeft className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {/* Header Content */}
            {currentView === 'main' && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <Headphones className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">الدعم الفني</h3>
                  <p className="text-xs text-slate-400">اختر مختص للمساعدة</p>
                </div>
              </>
            )}

            {currentView === 'chat' && selectedAgent && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {selectedAgent.avatar}
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">{selectedAgent.name}</h3>
                  <p className="text-xs text-slate-400">
                    {selectedAgent.isOnline ? (
                      <span className="text-green-400">متاح الآن</span>
                    ) : (
                      'غير متاح حالياً'
                    )}
                  </p>
                </div>
              </>
            )}

            {currentView === 'profile' && selectedAgent && (
              <>
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">الملف الشخصي</h3>
                  <p className="text-xs text-slate-400">{selectedAgent.name}</p>
                </div>
              </>
            )}
          </div>

          <div className="flex items-center gap-1">
            {/* Profile Button (only in chat view) */}
            {currentView === 'chat' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewProfile}
                className="p-1 hover:bg-slate-700"
              >
                <User className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {onToggleMinimize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="p-1 hover:bg-slate-700"
              >
                {isMinimized ? (
                  <Maximize2 className="h-4 w-4 text-slate-400" />
                ) : (
                  <Minimize2 className="h-4 w-4 text-slate-400" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1 hover:bg-slate-700"
            >
              <X className="h-4 w-4 text-slate-400" />
            </Button>
          </div>
        </div>

        {/* Animated Content Container */}
        {!isMinimized && (
          <div className="relative h-80 overflow-hidden">
            {/* Main Agent Selection View */}
            <div className={`
              absolute inset-0 transition-transform duration-300 ease-in-out
              ${currentView === 'main' ? 'translate-x-0' : '-translate-x-full'}
              ${isAnimating ? 'opacity-50' : 'opacity-100'}
            `}>
              <AgentSelectionView
                agents={supportAgents}
                onAgentSelect={handleAgentSelect}
                isAnimating={isAnimating}
              />
            </div>

            {/* Chat Conversation View */}
            <div className={`
              absolute inset-0 transition-transform duration-300 ease-in-out
              ${currentView === 'chat' ? 'translate-x-0' :
                currentView === 'main' ? 'translate-x-full' : '-translate-x-full'}
              ${isAnimating ? 'opacity-50' : 'opacity-100'}
            `}>
              <ChatConversationView
                messages={messages}
                messageInput={messageInput}
                setMessageInput={setMessageInput}
                handleSendMessage={handleSendMessage}
                handleKeyPress={handleKeyPress}
                messagesEndRef={messagesEndRef}
                inputRef={inputRef}
                isLoadingMessages={isLoadingMessages}
                typingUsers={typingUsers}
                error={error}
                isOnline={isOnline}
                selectedAgent={selectedAgent}
                isAnimating={isAnimating}
              />
            </div>

            {/* Profile View */}
            <div className={`
              absolute inset-0 transition-transform duration-300 ease-in-out
              ${currentView === 'profile' ? 'translate-x-0' : 'translate-x-full'}
              ${isAnimating ? 'opacity-50' : 'opacity-100'}
            `}>
              <ProfileView
                agent={selectedAgent}
                isAnimating={isAnimating}
              />
            </div>
          </div>
        )}
      </div>
    </>
  )
}

// Agent Selection View Component
function AgentSelectionView({
  agents,
  onAgentSelect,
  isAnimating
}: {
  agents: any[],
  onAgentSelect: (agent: any) => void,
  isAnimating: boolean
}) {
  return (
    <ScrollArea className="h-full p-3">
      <div className="space-y-3">
        <div className="text-center mb-4">
          <Headphones className="h-12 w-12 text-blue-400 mx-auto mb-2" />
          <h3 className="text-white font-semibold">اختر مختص الدعم</h3>
          <p className="text-slate-400 text-sm">اختر المختص المناسب لمساعدتك</p>
        </div>

        {agents.map((agent) => (
          <div
            key={agent.id}
            onClick={() => !isAnimating && onAgentSelect(agent)}
            className={`
              p-3 bg-slate-700/30 hover:bg-slate-700/50 rounded-lg cursor-pointer
              transition-all duration-200 border border-slate-600/30
              ${isAnimating ? 'pointer-events-none opacity-50' : 'hover:scale-[1.02]'}
            `}
          >
            <div className="flex items-center gap-3">
              {/* Agent Avatar */}
              <div className="relative">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center text-white font-bold
                  ${agent.isOnline
                    ? 'bg-gradient-to-r from-green-500 to-green-600'
                    : 'bg-gradient-to-r from-slate-500 to-slate-600'
                  }
                `}>
                  {agent.avatar}
                </div>
                {agent.isOnline && (
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-800" />
                )}
              </div>

              {/* Agent Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-white truncate">{agent.name}</h4>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    <span className="text-xs text-yellow-400">{agent.rating}</span>
                  </div>
                </div>
                <p className="text-slate-400 text-sm mb-1">{agent.role}</p>
                <div className="flex items-center gap-2 text-xs">
                  <Clock className="h-3 w-3 text-slate-500" />
                  <span className="text-slate-500">متوسط الرد: {agent.responseTime}</span>
                </div>
              </div>

              {/* Status Badge */}
              <div className="text-right">
                <Badge
                  className={`text-xs ${
                    agent.isOnline
                      ? 'bg-green-500/20 text-green-400 border-green-500/30'
                      : 'bg-slate-500/20 text-slate-400 border-slate-500/30'
                  }`}
                >
                  {agent.isOnline ? 'متاح' : 'مشغول'}
                </Badge>
              </div>
            </div>

            {/* Specialties */}
            <div className="mt-2 flex flex-wrap gap-1">
              {agent.specialties.slice(0, 2).map((specialty: string, index: number) => (
                <span
                  key={index}
                  className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded"
                >
                  {specialty}
                </span>
              ))}
              {agent.specialties.length > 2 && (
                <span className="text-xs text-slate-500">+{agent.specialties.length - 2}</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  )
}

// Chat Conversation View Component
function ChatConversationView({
  messages,
  messageInput,
  setMessageInput,
  handleSendMessage,
  handleKeyPress,
  messagesEndRef,
  inputRef,
  isLoadingMessages,
  typingUsers,
  error,
  isOnline,
  selectedAgent,
  isAnimating
}: {
  messages: ChatMessage[],
  messageInput: string,
  setMessageInput: (value: string) => void,
  handleSendMessage: () => void,
  handleKeyPress: (e: React.KeyboardEvent) => void,
  messagesEndRef: React.RefObject<HTMLDivElement>,
  inputRef: React.RefObject<HTMLInputElement>,
  isLoadingMessages: boolean,
  typingUsers: string[],
  error: string | null,
  isOnline: boolean,
  selectedAgent: any,
  isAnimating: boolean
}) {
  // Message bubble component
  function MessageBubble({ message }: { message: ChatMessage }) {
    const isFromAgent = message.senderType === 'admin'

    return (
      <div className={`flex ${isFromAgent ? 'justify-start' : 'justify-end'} mb-3`}>
        <div className={`
          max-w-[80%] px-3 py-2 rounded-lg text-sm
          ${isFromAgent
            ? 'bg-slate-700 text-white rounded-bl-sm'
            : 'bg-blue-500 text-white rounded-br-sm'
          }
          animate-in slide-in-from-bottom-2 duration-200
        `}>
          <p className="leading-relaxed">{message.message}</p>
          <div className={`
            flex items-center gap-1 mt-1 text-xs
            ${isFromAgent ? 'text-slate-400' : 'text-blue-100'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            {!isFromAgent && (
              <div className="flex items-center">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Messages Area */}
      <ScrollArea className="flex-1 p-3">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-2 rounded mb-3 text-sm">
            {error}
          </div>
        )}

        {isLoadingMessages ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className={`
                w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-3
                ${selectedAgent?.isOnline
                  ? 'bg-gradient-to-r from-green-500 to-green-600'
                  : 'bg-gradient-to-r from-slate-500 to-slate-600'
                }
              `}>
                {selectedAgent?.avatar}
              </div>
              <h4 className="text-white font-semibold mb-1">{selectedAgent?.name}</h4>
              <p className="text-slate-400 text-sm mb-3">{selectedAgent?.role}</p>
              <p className="text-slate-400 text-sm">
                مرحباً! أنا {selectedAgent?.name}، كيف يمكنني مساعدتك اليوم؟
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageBubble key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}

        {/* Typing indicator */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start mb-3">
            <div className="bg-slate-700 px-3 py-2 rounded-lg text-sm text-slate-300">
              <div className="flex items-center gap-1">
                <div className="flex gap-1">
                  <div className="w-1 h-1 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-1 h-1 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-1 h-1 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-xs">{selectedAgent?.name} يكتب...</span>
              </div>
            </div>
          </div>
        )}
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t border-slate-700 p-3">
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={`اكتب رسالة إلى ${selectedAgent?.name}...`}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 text-sm"
            maxLength={1000}
            disabled={!isOnline || isAnimating}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || !isOnline || isAnimating}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3"
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Profile View Component
function ProfileView({
  agent,
  isAnimating
}: {
  agent: any,
  isAnimating: boolean
}) {
  if (!agent) return null

  return (
    <ScrollArea className="h-full p-4">
      <div className="space-y-6">
        {/* Profile Header */}
        <div className="text-center">
          <div className={`
            w-20 h-20 rounded-full flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg
            ${agent.isOnline
              ? 'bg-gradient-to-r from-green-500 to-green-600'
              : 'bg-gradient-to-r from-slate-500 to-slate-600'
            }
          `}>
            {agent.avatar}
          </div>
          <h3 className="text-xl font-bold text-white mb-1">{agent.name}</h3>
          <p className="text-slate-400 mb-2">{agent.role}</p>
          <div className="flex items-center justify-center gap-2">
            <div className={`w-2 h-2 rounded-full ${agent.isOnline ? 'bg-green-400' : 'bg-slate-500'}`} />
            <span className={`text-sm ${agent.isOnline ? 'text-green-400' : 'text-slate-400'}`}>
              {agent.isOnline ? 'متاح الآن' : 'غير متاح حالياً'}
            </span>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-slate-700/30 p-3 rounded-lg text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Star className="h-4 w-4 text-yellow-400 fill-current" />
              <span className="text-lg font-bold text-white">{agent.rating}</span>
            </div>
            <p className="text-xs text-slate-400">التقييم</p>
          </div>
          <div className="bg-slate-700/30 p-3 rounded-lg text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Clock className="h-4 w-4 text-blue-400" />
              <span className="text-sm font-bold text-white">{agent.responseTime}</span>
            </div>
            <p className="text-xs text-slate-400">متوسط الرد</p>
          </div>
        </div>

        {/* Specialties */}
        <div>
          <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
            <Shield className="h-4 w-4 text-blue-400" />
            التخصصات
          </h4>
          <div className="space-y-2">
            {agent.specialties.map((specialty: string, index: number) => (
              <div key={index} className="flex items-center gap-3 p-2 bg-slate-700/20 rounded">
                <div className="w-2 h-2 bg-blue-400 rounded-full" />
                <span className="text-slate-300 text-sm">{specialty}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Info */}
        <div>
          <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
            <Mail className="h-4 w-4 text-green-400" />
            معلومات الاتصال
          </h4>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-slate-700/20 rounded">
              <Mail className="h-4 w-4 text-slate-400" />
              <div>
                <p className="text-slate-300 text-sm">البريد الإلكتروني</p>
                <p className="text-slate-400 text-xs">{agent.name.toLowerCase().replace(' ', '.')}@alraya-store.com</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-slate-700/20 rounded">
              <Phone className="h-4 w-4 text-slate-400" />
              <div>
                <p className="text-slate-300 text-sm">الهاتف المباشر</p>
                <p className="text-slate-400 text-xs">+966 50 123 {Math.floor(Math.random() * 9000) + 1000}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Working Hours */}
        <div>
          <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
            <Clock className="h-4 w-4 text-purple-400" />
            ساعات العمل
          </h4>
          <div className="bg-slate-700/20 p-3 rounded">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-300">السبت - الخميس</span>
                <span className="text-slate-400">9:00 ص - 11:00 م</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">الجمعة</span>
                <span className="text-slate-400">2:00 م - 11:00 م</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-2">
          <Button
            className="w-full bg-blue-500 hover:bg-blue-600 text-white"
            disabled={isAnimating}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            بدء محادثة جديدة
          </Button>
          <Button
            variant="outline"
            className="w-full border-slate-600 text-slate-300 hover:text-white"
            disabled={isAnimating}
          >
            <Star className="h-4 w-4 mr-2" />
            تقييم المختص
          </Button>
        </div>
      </div>
    </ScrollArea>
  )
}
