"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Image from "next/image"
import { useRouter } from "next/navigation"
import {
  LayoutDashboard,
  Package,
  FolderOpen,
  Settings,
  Home,
  User,
  LogOut,
  Bell,
  Search,
  Menu,
  X,
  Phone,
  ShoppingCart,
  MessageSquare
} from "lucide-react"
import { ChatBadge } from '@/components/chat/ChatSystem'
import { AdminChatButton } from '@/components/chat/AdminChatButton'

interface AdminHeaderProps {
  activeTab: "overview" | "categories" | "products" | "orders" | "settings" | "contact" | "chat"
  onTabChange: (tab: "overview" | "categories" | "products" | "orders" | "settings" | "contact" | "chat") => void
  // ## Chat Integration: Unread message count
  unreadChatCount?: number
}

export function AdminHeader({ activeTab, onTabChange, unreadChatCount = 0 }: AdminHeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const router = useRouter()

  const handleHomeNavigation = () => {
    router.push("/")
  }

  const handleProfileNavigation = () => {
    router.push("/profile")
  }

  // Fixed navigationTabs to prevent hydration mismatch
  const navigationTabs = [
    { id: "overview", label: "نظرة عامة", icon: LayoutDashboard, shortLabel: "عام" },
    { id: "categories", label: "الفئات", icon: FolderOpen, shortLabel: "فئات" },
    { id: "products", label: "المنتجات", icon: Package, shortLabel: "منتجات" },
    { id: "orders", label: "الطلبات", icon: ShoppingCart, shortLabel: "طلبات" },
    {
      id: "chat",
      label: "المحادثات",
      icon: MessageSquare,
      shortLabel: "محادثات",
      badge: unreadChatCount > 0 ? unreadChatCount : undefined
    },
    { id: "contact", label: "صفحة الاتصال", icon: Phone, shortLabel: "الاتصال" },
    { id: "settings", label: "الإعدادات", icon: Settings, shortLabel: "إعدادات" }
  ] as const

  return (
    <header className="bg-slate-800/95 border-b border-slate-700/50 backdrop-blur-xl sticky top-0 z-50 shadow-2xl">
      <div className="max-w-7xl mx-auto">
        {/* Main Header Row */}
        <div className="flex items-center justify-between px-4 lg:px-6 py-4 lg:py-5">
          {/* Left Section - Logo & Title */}
          <div className="flex items-center gap-4 lg:gap-6">
            <div
              className="flex items-center gap-3 cursor-pointer hover:opacity-80 transition-all duration-300 group"
              onClick={handleHomeNavigation}
            >
              <Image
                src="/logo-without-background.png"
                alt="رايه شوب"
                width={140}
                height={56}
                className="h-10 lg:h-12 w-auto object-contain group-hover:scale-105 transition-transform duration-300"
                priority
              />
            </div>
            
            <div className="h-8 w-px bg-slate-600/50 hidden lg:block"></div>
            
            <div className="hidden lg:block">
              <h1 className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                لوحة التحكم الإدارية
              </h1>
              <p className="text-slate-400 text-sm mt-1">إدارة شاملة للمنتجات والطلبات</p>
            </div>
          </div>

          {/* Right Section - Actions & Profile */}
          <div className="flex items-center gap-3 lg:gap-4">
            {/* Search Button - Desktop Only */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden lg:flex text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Notifications */}
            <Button
              variant="ghost"
              size="icon"
              className="relative text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300"
            >
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs"></span>
            </Button>

            {/* Home Button */}
            <Button
              onClick={handleHomeNavigation}
              variant="outline"
              size="sm"
              className="hidden lg:flex bg-slate-700/50 border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-600/50 hover:border-slate-500/50 transition-all duration-300"
            >
              <Home className="h-4 w-4 mr-2" />
              الرئيسية
            </Button>

            {/* Status Badge */}
            <Badge className="hidden lg:flex bg-green-500/20 text-green-400 border-green-500/30 px-3 py-1">
              متصل
            </Badge>

            {/* Profile Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                  <Avatar className="h-10 w-10 border-2 border-yellow-400/20 hover:border-yellow-400/40 transition-all duration-300">
                    <AvatarImage src="" alt="Admin" />
                    <AvatarFallback className="bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-sm font-bold">
                      أ
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 bg-slate-800 border-slate-700" align="end" forceMount>
                <DropdownMenuItem onClick={handleProfileNavigation} className="text-slate-300 hover:text-white hover:bg-slate-700">
                  <User className="mr-2 h-4 w-4" />
                  <span>الملف الشخصي</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleHomeNavigation} className="text-slate-300 hover:text-white hover:bg-slate-700">
                  <Home className="mr-2 h-4 w-4" />
                  <span>الصفحة الرئيسية</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-slate-700" />
                <DropdownMenuItem className="text-red-400 hover:text-red-300 hover:bg-slate-700">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>تسجيل الخروج</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile Menu Toggle */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Navigation Tabs Row - Desktop */}
        <div className="hidden lg:block border-t border-slate-700/30 bg-slate-800/30">
          <div className="flex items-center px-4 lg:px-6 py-4">
            <nav className="flex items-center gap-3">
              {navigationTabs.map((tab) => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onTabChange(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold shadow-lg border-0"
                      : "text-slate-300 hover:text-white hover:bg-slate-700/50 border-0"
                  } px-5 py-3 h-auto transition-all duration-300 hover:scale-105 rounded-lg font-medium relative`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                  {tab.badge && (
                    <ChatBadge
                      count={tab.badge}
                      className="absolute -top-2 -right-2 scale-75"
                    />
                  )}
                </Button>
              ))}
            </nav>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-slate-700/30 bg-slate-800/95 backdrop-blur-xl">
            <div className="px-4 py-4 space-y-2">
              {/* Mobile Title */}
              <div className="mb-4">
                <h2 className="text-lg font-bold text-white">لوحة التحكم</h2>
                <p className="text-slate-400 text-sm">إدارة المنتجات والطلبات</p>
              </div>

              {/* Mobile Navigation */}
              <nav className="space-y-1">
                {navigationTabs.map((tab) => (
                  <Button
                    key={tab.id}
                    variant={activeTab === tab.id ? "default" : "ghost"}
                    size="sm"
                    onClick={() => {
                      onTabChange(tab.id)
                      setIsMobileMenuOpen(false)
                    }}
                    className={`${
                      activeTab === tab.id
                        ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold"
                        : "text-slate-300 hover:text-white hover:bg-slate-700/50"
                    } w-full justify-start px-4 py-3 h-auto relative`}
                  >
                    <tab.icon className="h-4 w-4 mr-3" />
                    {tab.label}
                    {tab.badge && (
                      <ChatBadge
                        count={tab.badge}
                        className="absolute top-1 left-1 scale-75"
                      />
                    )}
                  </Button>
                ))}
              </nav>

              {/* Mobile Actions */}
              <div className="pt-4 border-t border-slate-700/30 space-y-2">
                <Button
                  onClick={() => {
                    handleHomeNavigation()
                    setIsMobileMenuOpen(false)
                  }}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start bg-slate-700/50 border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-600/50"
                >
                  <Home className="h-4 w-4 mr-3" />
                  الصفحة الرئيسية
                </Button>
                
                <div className="flex items-center justify-between pt-2">
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                    متصل
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Floating Chat Button */}
      <AdminChatButton
        userId="admin-demo"
        userName="مدير النظام"
        userEmail="<EMAIL>"
        position="bottom-right"
      />
    </header>
  )
}
