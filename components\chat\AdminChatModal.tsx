/**
 * ## Admin Chat Modal - WhatsApp Style Popup
 * Modal/popup version for integration into admin dashboard
 * Compact, efficient design for overlay usage
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  MessageSquare, 
  Send, 
  Search, 
  Users, 
  X,
  Minimize2,
  Maximize2,
  User,
  Package,
  Shield
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage, ChatRoom } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface AdminChatModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName?: string
  userEmail?: string
  // Position and size control
  position?: 'bottom-right' | 'bottom-left' | 'center'
  isMinimized?: boolean
  onToggleMinimize?: () => void
}

export function AdminChatModal({ 
  isOpen,
  onClose,
  userId, 
  userName, 
  userEmail,
  position = 'bottom-right',
  isMinimized = false,
  onToggleMinimize
}: AdminChatModalProps) {
  const [selectedChatUserId, setSelectedChatUserId] = useState<string | null>(null)
  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [showCustomerDetails, setShowCustomerDetails] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Chat hook
  const {
    messages,
    chatRooms,
    isLoadingMessages,
    isLoadingRooms,
    sendMessage,
    markAsRead,
    typingUsers,
    unreadCount,
    error
  } = useChat({
    userId,
    userType: 'admin',
    selectedChatUserId: selectedChatUserId || undefined
  })

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Handle customer selection
  const handleSelectCustomer = (customerId: string) => {
    setSelectedChatUserId(customerId)
    const unreadMessages = messages
      .filter(msg => !msg.isRead && msg.senderType === 'customer' && msg.userId === customerId)
      .map(msg => msg.id)
    if (unreadMessages.length > 0) {
      markAsRead(unreadMessages)
    }
  }

  // Handle message sending
  const handleSendMessage = async () => {
    if (!messageInput.trim() || !selectedChatUserId) return
    const message = messageInput.trim()
    setMessageInput('')
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  // Filter chat rooms
  const filteredChatRooms = chatRooms.filter(room =>
    room.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    room.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const selectedCustomer = chatRooms.find(room => room.userId === selectedChatUserId)

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
      default:
        return 'bottom-4 right-4'
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />

      {/* Chat Modal */}
      <div className={`
        fixed ${getPositionClasses()} z-50
        ${isMinimized ? 'w-80 h-16' : 'w-96 h-[600px]'}
        bg-slate-800 rounded-lg shadow-2xl border border-slate-700
        transition-all duration-300 ease-in-out
        max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]
      `}>
        
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700 bg-slate-800 rounded-t-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
              <MessageSquare className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-white text-sm">
                {selectedCustomer ? selectedCustomer.customerName : 'المحادثات'}
              </h3>
              {selectedCustomer && (
                <p className="text-xs text-slate-400">
                  {selectedCustomer.isOnline ? 'متصل الآن' : 'غير متصل'}
                </p>
              )}
            </div>
            {unreadCount > 0 && (
              <Badge className="bg-green-500 text-white text-xs px-1.5 py-0.5">
                {unreadCount}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-1">
            {onToggleMinimize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="p-1.5 hover:bg-slate-700"
              >
                {isMinimized ? (
                  <Maximize2 className="h-4 w-4 text-slate-400" />
                ) : (
                  <Minimize2 className="h-4 w-4 text-slate-400" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1.5 hover:bg-slate-700"
            >
              <X className="h-4 w-4 text-slate-400" />
            </Button>
          </div>
        </div>

        {/* Content - Hidden when minimized */}
        {!isMinimized && (
          <div className="flex h-[calc(100%-4rem)]">
            {/* Customer List */}
            <div className={`${
              selectedChatUserId ? 'hidden md:flex md:w-48' : 'flex w-full md:w-48'
            } border-r border-slate-700 flex-col`}>
              
              {/* Search */}
              <div className="p-3 border-b border-slate-700">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="بحث..."
                    className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 pl-8 text-xs h-8"
                  />
                </div>
              </div>

              {/* Customer List */}
              <ScrollArea className="flex-1">
                {isLoadingRooms ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400"></div>
                  </div>
                ) : filteredChatRooms.length === 0 ? (
                  <div className="text-center p-6">
                    <MessageSquare className="h-8 w-8 text-slate-500 mx-auto mb-2" />
                    <p className="text-slate-400 text-xs">لا توجد محادثات</p>
                  </div>
                ) : (
                  <div className="divide-y divide-slate-700/30">
                    {filteredChatRooms.map((room) => (
                      <div
                        key={room.userId}
                        onClick={() => handleSelectCustomer(room.userId)}
                        className={`
                          p-3 cursor-pointer transition-all duration-200 
                          hover:bg-slate-700/30
                          ${selectedChatUserId === room.userId ? 'bg-slate-700/50' : ''}
                        `}
                      >
                        <div className="flex items-center gap-2">
                          <div className="relative flex-shrink-0">
                            <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                              {room.customerName.charAt(0)}
                            </div>
                            {room.isOnline && (
                              <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-400 rounded-full border border-slate-800" />
                            )}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-white truncate text-xs">
                                {room.customerName}
                              </h4>
                              {room.unreadCount > 0 && (
                                <div className="bg-green-500 text-white text-xs font-bold rounded-full min-w-[16px] h-4 flex items-center justify-center px-1">
                                  {room.unreadCount > 9 ? '9+' : room.unreadCount}
                                </div>
                              )}
                            </div>
                            
                            {room.lastMessage && (
                              <p className="text-xs text-slate-400 truncate mt-0.5">
                                {room.lastMessage.senderType === 'admin' && (
                                  <span className="text-blue-400">أنت: </span>
                                )}
                                {room.lastMessage.message}
                              </p>
                            )}
                            
                            {room.activeOrders.length > 0 && (
                              <div className="flex items-center gap-1 mt-1">
                                <div className="w-1.5 h-1.5 bg-orange-400 rounded-full" />
                                <span className="text-xs text-orange-400">
                                  {room.activeOrders.length} طلب
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>

            {/* Chat Area */}
            <div className={`${
              selectedChatUserId ? 'flex w-full' : 'hidden md:flex md:flex-1'
            } flex-col`}>
              {selectedChatUserId ? (
                <>
                  {/* Chat Header */}
                  <div className="p-3 border-b border-slate-700 bg-slate-800/50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedChatUserId(null)}
                          className="md:hidden p-1"
                        >
                          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </Button>
                        
                        <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                          {selectedCustomer?.customerName.charAt(0)}
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white text-sm truncate">
                            {selectedCustomer?.customerName}
                          </h4>
                          {typingUsers.length > 0 && (
                            <p className="text-xs text-green-400">يكتب...</p>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowCustomerDetails(true)}
                        className="p-1"
                      >
                        <User className="h-4 w-4 text-slate-400" />
                      </Button>
                    </div>
                  </div>

                  {/* Messages */}
                  <ScrollArea className="flex-1 p-3 bg-slate-900/20">
                    {error && (
                      <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-2 rounded text-xs mb-3">
                        {error}
                      </div>
                    )}

                    {isLoadingMessages ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400"></div>
                      </div>
                    ) : messages.length === 0 ? (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <MessageSquare className="h-8 w-8 text-slate-500 mx-auto mb-2" />
                          <p className="text-slate-400 text-xs">ابدأ محادثة</p>
                        </div>
                      </div>
                    ) : (
                      <>
                        {messages.map((message) => (
                          <MessageBubble key={message.id} message={message} />
                        ))}
                        <div ref={messagesEndRef} />
                      </>
                    )}
                  </ScrollArea>

                  {/* Input */}
                  <div className="border-t border-slate-700 p-3">
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <Input
                          ref={inputRef}
                          value={messageInput}
                          onChange={(e) => setMessageInput(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault()
                              handleSendMessage()
                            }
                          }}
                          placeholder="اكتب رسالة..."
                          className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 text-sm h-8"
                          maxLength={1000}
                        />
                      </div>
                      
                      <Button
                        onClick={handleSendMessage}
                        disabled={!messageInput.trim()}
                        className="bg-green-500 hover:bg-green-600 text-white p-2 h-8 w-8"
                        size="sm"
                      >
                        <Send className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="hidden md:flex flex-1 items-center justify-center">
                  <div className="text-center">
                    <MessageSquare className="h-12 w-12 text-slate-500 mx-auto mb-3" />
                    <p className="text-slate-400 text-sm">اختر محادثة</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Customer Details Modal */}
      {showCustomerDetails && selectedCustomer && (
        <div className="fixed inset-0 bg-black/50 z-60 flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-lg w-full max-w-sm">
            <div className="p-4 border-b border-slate-700">
              <div className="flex items-center justify-between">
                <h3 className="font-bold text-white">معلومات العميل</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCustomerDetails(false)}
                  className="p-1"
                >
                  <X className="h-4 w-4 text-slate-400" />
                </Button>
              </div>
            </div>

            <div className="p-4">
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-3">
                  {selectedCustomer.customerName.charAt(0)}
                </div>
                <h4 className="font-bold text-white">{selectedCustomer.customerName}</h4>
                <p className="text-slate-400 text-sm">{selectedCustomer.customerEmail}</p>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between p-2 bg-slate-700/30 rounded text-sm">
                  <span className="text-slate-400">الحالة</span>
                  <span className="text-white">
                    {selectedCustomer.isOnline ? 'متصل' : 'غير متصل'}
                  </span>
                </div>

                <div className="flex justify-between p-2 bg-slate-700/30 rounded text-sm">
                  <span className="text-slate-400">الطلبات النشطة</span>
                  <span className="text-orange-400">
                    {selectedCustomer.activeOrders.length} طلب
                  </span>
                </div>
              </div>

              <div className="mt-4 space-y-2">
                <Button 
                  className="w-full bg-green-500 hover:bg-green-600 text-white text-sm"
                  onClick={() => setShowCustomerDetails(false)}
                >
                  <Package className="h-3 w-3 mr-2" />
                  عرض الطلبات
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )

  function MessageBubble({ message }: { message: ChatMessage }) {
    const isFromAdmin = message.senderType === 'admin'

    return (
      <div className={`flex ${isFromAdmin ? 'justify-end' : 'justify-start'} mb-2`}>
        <div className={`
          max-w-[80%] px-3 py-2 rounded-lg text-xs
          ${isFromAdmin 
            ? 'bg-green-500 text-white rounded-br-sm' 
            : 'bg-white text-slate-900 rounded-bl-sm'
          }
        `}>
          <p className="leading-relaxed">{message.message}</p>
          <div className={`text-xs mt-1 ${isFromAdmin ? 'text-green-100' : 'text-slate-500'}`}>
            {formatDate(message.createdAt, 'time')}
          </div>
        </div>
      </div>
    )
  }
}
