/**
 * ## Customer Chat Button - Floating Action Button
 * Floating button for customers to access support chat
 * Shows unread count and provides quick access to customer support
 */

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageSquare, X, Minimize2, Maximize2, Headphones } from 'lucide-react'
import { CustomerChatModal } from './CustomerChatModal'
import { useChat } from '@/lib/hooks/useChat'

interface CustomerChatButtonProps {
  userId: string
  userName?: string
  userEmail?: string
  // Position of the floating button
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  // Custom styling
  className?: string
}

export function CustomerChatButton({ 
  userId, 
  userName, 
  userEmail,
  position = 'bottom-right',
  className = ''
}: CustomerChatButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [hasNewMessages, setHasNewMessages] = useState(false)

  // Get unread count from chat hook
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  // Animate button when new messages arrive
  useEffect(() => {
    if (unreadCount > 0 && !isModalOpen) {
      setHasNewMessages(true)
      const timer = setTimeout(() => setHasNewMessages(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [unreadCount, isModalOpen])

  // Position classes for floating button
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6'
      case 'top-right':
        return 'top-6 right-6'
      case 'top-left':
        return 'top-6 left-6'
      default:
        return 'bottom-6 right-6'
    }
  }

  return (
    <>
      {/* Floating Chat Button */}
      {!isModalOpen && (
        <div className={`fixed ${getPositionClasses()} z-40 ${className}`}>
          <Button
            onClick={() => setIsModalOpen(true)}
            className={`
              w-14 h-14 rounded-full shadow-lg
              bg-gradient-to-r from-blue-500 to-blue-600
              hover:from-blue-600 hover:to-blue-700
              text-white border-0
              transition-all duration-300
              ${hasNewMessages ? 'animate-bounce' : 'hover:scale-110'}
              focus:outline-none focus:ring-4 focus:ring-blue-500/30
            `}
            aria-label="الدعم الفني"
          >
            <Headphones className="w-6 h-6" />
            
            {/* Unread Badge */}
            {unreadCount > 0 && (
              <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-1.5 py-0.5 min-w-[20px] h-5 flex items-center justify-center animate-pulse">
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}

            {/* Pulse animation for new messages */}
            {hasNewMessages && (
              <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-75" />
            )}
          </Button>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
              الدعم الفني {unreadCount > 0 && `(${unreadCount} رسالة جديدة)`}
            </div>
          </div>
        </div>
      )}

      {/* Chat Modal */}
      <CustomerChatModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false)
          setIsMinimized(false)
        }}
        userId={userId}
        userName={userName}
        userEmail={userEmail}
        position={position === 'bottom-left' || position === 'top-left' ? 'bottom-left' : 'bottom-right'}
        isMinimized={isMinimized}
        onToggleMinimize={() => setIsMinimized(!isMinimized)}
      />
    </>
  )
}

/**
 * ## Customer Support Widget - Integrated Version
 * For embedding directly in customer pages
 */
interface CustomerSupportWidgetProps {
  userId: string
  userName?: string
  userEmail?: string
  className?: string
}

export function CustomerSupportWidget({ 
  userId, 
  userName, 
  userEmail,
  className = ''
}: CustomerSupportWidgetProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  return (
    <div className={`bg-slate-800 rounded-lg border border-slate-700 ${className}`}>
      {/* Widget Header */}
      <div 
        className="p-4 border-b border-slate-700 cursor-pointer hover:bg-slate-700/30 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
              <Headphones className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-white">الدعم الفني</h3>
              <p className="text-xs text-slate-400">
                {unreadCount > 0 ? `${unreadCount} رسالة جديدة` : 'متاح للمساعدة'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Badge className="bg-blue-500 text-white text-xs">
                {unreadCount}
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="p-1"
            >
              {isExpanded ? (
                <Minimize2 className="h-4 w-4 text-slate-400" />
              ) : (
                <Maximize2 className="h-4 w-4 text-slate-400" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Widget Content */}
      {isExpanded && (
        <div className="h-96">
          <CustomerChatModal
            isOpen={true}
            onClose={() => setIsExpanded(false)}
            userId={userId}
            userName={userName}
            userEmail={userEmail}
            position="center"
            isMinimized={false}
          />
        </div>
      )}
    </div>
  )
}

/**
 * ## Usage Examples:
 * 
 * // Floating button (recommended for most customer pages)
 * <CustomerChatButton userId="customer-123" />
 * 
 * // Integrated widget (for customer dashboard)
 * <CustomerSupportWidget userId="customer-123" className="col-span-1" />
 */
