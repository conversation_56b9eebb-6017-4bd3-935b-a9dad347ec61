/**
 * ## Customer Chat Demo Page
 * Demonstrates the new integrated customer chat interface
 * Shows smooth animations between agent selection, chat, and profile views
 */

'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CustomerChatButton } from '@/components/chat/CustomerChatButton'
import { 
  MessageSquare, 
  Users, 
  Star,
  Clock,
  Headphones,
  ArrowRight
} from 'lucide-react'

export default function CustomerChatDemoPage() {
  const [showChatButton, setShowChatButton] = useState(true)

  // Mock customer data
  const customerData = {
    userId: 'customer-demo',
    userName: 'أحمد محمد',
    userEmail: '<EMAIL>'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-6xl mx-auto">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            نظام الدعم الفني المتطور - عرض توضيحي
          </h1>
          <p className="text-slate-400">
            تجربة نظام الدعم الفني الجديد مع التنقل السلس بين المختصين والمحادثات والملفات الشخصية
          </p>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-white font-semibold mb-2">اختيار المختص</h3>
              <p className="text-slate-400 text-sm">
                اختر من بين مختصي الدعم المتاحين حسب التخصص والتقييم
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageSquare className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-white font-semibold mb-2">محادثة فورية</h3>
              <p className="text-slate-400 text-sm">
                تواصل مباشر مع المختص مع إشعارات الكتابة وحالة القراءة
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-white font-semibold mb-2">ملف المختص</h3>
              <p className="text-slate-400 text-sm">
                عرض تفاصيل المختص والتخصصات ومعلومات الاتصال
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Demo Instructions */}
        <Card className="bg-slate-800/50 border-slate-700/50 mb-8">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Headphones className="h-5 w-5" />
              كيفية تجربة النظام
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  1
                </div>
                <div>
                  <h4 className="text-white font-medium">انقر على زر الدعم الفني</h4>
                  <p className="text-slate-400 text-sm">ابحث عن الزر الأزرق في أسفل يمين الشاشة</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  2
                </div>
                <div>
                  <h4 className="text-white font-medium">اختر مختص الدعم</h4>
                  <p className="text-slate-400 text-sm">انقر على أي مختص من القائمة لبدء المحادثة</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  3
                </div>
                <div>
                  <h4 className="text-white font-medium">استكشف الميزات</h4>
                  <p className="text-slate-400 text-sm">انقر على أيقونة الملف الشخصي لعرض تفاصيل المختص</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  4
                </div>
                <div>
                  <h4 className="text-white font-medium">التنقل السلس</h4>
                  <p className="text-slate-400 text-sm">استخدم أزرار الرجوع للتنقل بين الشاشات بسلاسة</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white">الميزات الجديدة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-green-400" />
                <span className="text-slate-300">انتقالات سلسة بين الشاشات</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-green-400" />
                <span className="text-slate-300">اختيار المختص حسب التخصص</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-green-400" />
                <span className="text-slate-300">عرض تفاصيل المختص والتقييمات</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-green-400" />
                <span className="text-slate-300">واجهة محادثة محسنة</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-green-400" />
                <span className="text-slate-300">تصميم متجاوب للجوال</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white">التحسينات التقنية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-blue-400" />
                <span className="text-slate-300">رسوم متحركة CSS محسنة</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-blue-400" />
                <span className="text-slate-300">إدارة حالة محسنة</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-blue-400" />
                <span className="text-slate-300">تجربة مستخدم سلسة</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-blue-400" />
                <span className="text-slate-300">تحميل سريع للمحتوى</span>
              </div>
              <div className="flex items-center gap-3">
                <ArrowRight className="h-4 w-4 text-blue-400" />
                <span className="text-slate-300">دعم كامل للغة العربية</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Button Toggle */}
        <div className="mt-8 text-center">
          <Button
            onClick={() => setShowChatButton(!showChatButton)}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:text-white"
          >
            {showChatButton ? 'إخفاء زر الدعم' : 'إظهار زر الدعم'}
          </Button>
        </div>

        {/* Customer Chat Button */}
        {showChatButton && (
          <CustomerChatButton
            userId={customerData.userId}
            userName={customerData.userName}
            userEmail={customerData.userEmail}
            position="bottom-right"
          />
        )}
      </div>
    </div>
  )
}
