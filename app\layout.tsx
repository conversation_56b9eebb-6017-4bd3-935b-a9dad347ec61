import type React from "react"
import type { Metada<PERSON> } from "next"
import { Cairo } from "next/font/google"
import "./globals.css"

const cairo = Cairo({
  subsets: ["arabic"],
  display: "swap",
})

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  title: "رايه شوب - أفضل متجر للشحن الفوري",
  description: "رايه شوب - متجرك الموثوق لشحن الألعاب والبطاقات الرقمية بأفضل الأسعار وأسرع خدمة",
  keywords: "شحن ببجي, بطاقات آيتونز, شحن فوري, ألعاب, بطاقات رقمية",
  icons: {
    icon: [
      { url: '/favicon.ico', type: 'image/x-icon' },
      { url: '/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icon-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'manifest', url: '/site.webmanifest' },
    ],
  },
  openGraph: {
    title: "رايه شوب - أفضل متجر للشحن الفوري",
    description: "متجرك الموثوق لشحن الألعاب والبطاقات الرقمية",
    type: "website",
    url: "https://raya-shop.com",
    siteName: "رايه شوب",
    images: [
      {
        url: '/logo-with-background.png',
        width: 1200,
        height: 630,
        alt: 'رايه شوب - أفضل متجر للشحن الفوري',
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "رايه شوب - أفضل متجر للشحن الفوري",
    description: "متجرك الموثوق لشحن الألعاب والبطاقات الرقمية",
    images: ['/logo-with-background.png'],
  },
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="dark">
      <body className={cairo.className}>{children}</body>
    </html>
  )
}
