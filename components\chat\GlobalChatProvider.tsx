/**
 * ## Global Chat Provider
 * Provides chat functionality across all pages of the website
 * Automatically detects user role and shows appropriate chat interface
 */

'use client'

import { useState, useEffect, createContext, useContext } from 'react'
import { AdminChatButton } from './AdminChatButton'
import { CustomerChatButton } from './CustomerChatButton'

interface ChatUser {
  id: string
  name: string
  email: string
  role: 'admin' | 'customer' | 'guest'
}

interface GlobalChatContextType {
  user: ChatUser | null
  isEnabled: boolean
  setUser: (user: ChatUser | null) => void
  setEnabled: (enabled: boolean) => void
}

const GlobalChatContext = createContext<GlobalChatContextType | null>(null)

export function useGlobalChat() {
  const context = useContext(GlobalChatContext)
  if (!context) {
    throw new Error('useGlobalChat must be used within GlobalChatProvider')
  }
  return context
}

interface GlobalChatProviderProps {
  children: React.ReactNode
}

export function GlobalChatProvider({ children }: GlobalChatProviderProps) {
  const [user, setUser] = useState<ChatUser | null>(null)
  const [isEnabled, setEnabled] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)

  /**
   * ## Initialize User Detection
   * Detects if user is admin or customer based on current route and auth
   */
  useEffect(() => {
    const initializeUser = async () => {
      try {
        // ## TODO: Replace with actual Supabase auth
        // const { data: { user }, error } = await supabase.auth.getUser()
        // if (user) {
        //   const { data: profile } = await supabase
        //     .from('profiles')
        //     .select('role, full_name')
        //     .eq('id', user.id)
        //     .single()
        //   
        //   setUser({
        //     id: user.id,
        //     name: profile?.full_name || user.email || 'مستخدم',
        //     email: user.email || '',
        //     role: profile?.role || 'customer'
        //   })
        // }

        // Demo user detection based on current path
        const currentPath = window.location.pathname
        
        if (currentPath.startsWith('/admin')) {
          // Admin user
          setUser({
            id: 'admin-demo',
            name: 'مدير النظام',
            email: '<EMAIL>',
            role: 'admin'
          })
        } else {
          // Regular customer/guest
          setUser({
            id: 'customer-demo',
            name: 'عميل',
            email: '<EMAIL>',
            role: 'customer'
          })
        }
      } catch (error) {
        console.error('Error initializing chat user:', error)
        // Set as guest user
        setUser({
          id: 'guest-' + Date.now(),
          name: 'زائر',
          email: '',
          role: 'guest'
        })
      } finally {
        setIsInitialized(true)
      }
    }

    initializeUser()
  }, [])

  /**
   * ## Update user when route changes
   */
  useEffect(() => {
    const handleRouteChange = () => {
      const currentPath = window.location.pathname
      
      if (currentPath.startsWith('/admin') && user?.role !== 'admin') {
        setUser({
          id: 'admin-demo',
          name: 'مدير النظام',
          email: '<EMAIL>',
          role: 'admin'
        })
      } else if (!currentPath.startsWith('/admin') && user?.role === 'admin') {
        setUser({
          id: 'customer-demo',
          name: 'عميل',
          email: '<EMAIL>',
          role: 'customer'
        })
      }
    }

    // Listen for route changes
    window.addEventListener('popstate', handleRouteChange)
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange)
    }
  }, [user])

  if (!isInitialized) {
    return <>{children}</>
  }

  return (
    <GlobalChatContext.Provider value={{ user, isEnabled, setUser, setEnabled }}>
      {children}
      
      {/* Global Chat Button - Only for customers, not admin pages */}
      {isEnabled && user && user.role === 'customer' && (
        <CustomerChatButton
          userId={user.id}
          userName={user.name}
          userEmail={user.email}
          position="bottom-right"
        />
      )}
    </GlobalChatContext.Provider>
  )
}
