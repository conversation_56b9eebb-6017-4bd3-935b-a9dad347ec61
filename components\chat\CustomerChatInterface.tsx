/**
 * ## Customer Chat Interface
 * Simple, clean chat interface for customers
 * Focuses on ease of use and clear communication
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  MessageCircle, 
  Send, 
  Clock, 
  CheckCircle2, 
  Circle,
  Paperclip,
  Image as ImageIcon
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage } from '@/lib/types'
import { formatDate } from '@/lib/utils/dateUtils'

interface CustomerChatInterfaceProps {
  userId: string
  userName?: string
  userEmail?: string
}

export function CustomerChatInterface({ 
  userId, 
  userName, 
  userEmail 
}: CustomerChatInterfaceProps) {
  const [messageInput, setMessageInput] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Chat hook
  const {
    messages,
    isLoadingMessages,
    sendMessage,
    markAsRead,
    isOnline,
    typingUsers,
    error
  } = useChat({
    userId,
    userType: 'customer'
  })

  /**
   * ## Auto-scroll to latest message
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  /**
   * ## Auto-mark messages as read when visible
   */
  useEffect(() => {
    const unreadMessages = messages
      .filter(msg => !msg.isRead && msg.senderType === 'admin')
      .map(msg => msg.id)
    
    if (unreadMessages.length > 0) {
      markAsRead(unreadMessages)
    }
  }, [messages, markAsRead])

  /**
   * ## Handle message sending
   */
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return

    const message = messageInput.trim()
    setMessageInput('')
    
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  /**
   * ## Handle typing indicator
   */
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true)
      // ## TODO: Send typing indicator to admin
      // supabase.channel('typing').send({
      //   type: 'broadcast',
      //   event: 'typing_start',
      //   payload: { userId, userType: 'customer' }
      // })
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      // ## TODO: Send typing stopped to admin
      // supabase.channel('typing').send({
      //   type: 'broadcast',
      //   event: 'typing_stop',
      //   payload: { userId, userType: 'customer' }
      // })
    }, 2000)
  }

  /**
   * ## Handle Enter key press
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  /**
   * ## Message Component
   */
  const MessageBubble = ({ message }: { message: ChatMessage }) => {
    const isFromAdmin = message.senderType === 'admin'
    const isSystemMessage = message.messageType === 'system'

    if (isSystemMessage) {
      return (
        <div className="flex justify-center my-4">
          <div className="bg-slate-700/50 text-slate-300 text-sm px-3 py-1 rounded-full">
            {message.message}
          </div>
        </div>
      )
    }

    return (
      <div className={`flex ${isFromAdmin ? 'justify-start' : 'justify-end'} mb-4`}>
        <div className={`
          max-w-[80%] px-4 py-3 rounded-2xl
          ${isFromAdmin 
            ? 'bg-slate-700/50 text-white rounded-bl-md' 
            : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md'
          }
          animate-in slide-in-from-bottom-2 duration-300
        `}>
          {/* Message content */}
          <p className="text-sm leading-relaxed">{message.message}</p>
          
          {/* Message metadata */}
          <div className={`
            flex items-center gap-2 mt-2 text-xs
            ${isFromAdmin ? 'text-slate-400' : 'text-blue-100'}
          `}>
            <span>{formatDate(message.createdAt, 'time')}</span>
            
            {!isFromAdmin && (
              <div className="flex items-center">
                {message.isRead ? (
                  <CheckCircle2 className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  /**
   * ## Admin Status Indicator
   */
  const AdminStatus = () => (
    <div className="flex items-center gap-2 text-sm text-slate-400 mb-4">
      <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-slate-500'}`} />
      <span>
        {isOnline ? 'الدعم الفني متاح الآن' : 'الدعم الفني غير متاح حالياً'}
      </span>
      {typingUsers.length > 0 && (
        <span className="text-blue-400 animate-pulse">
          يكتب الآن...
        </span>
      )}
    </div>
  )

  /**
   * ## Loading State
   */
  if (isLoadingMessages) {
    return (
      <Card className="bg-slate-800/50 border-slate-700/50 h-[600px]">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-slate-300">جاري تحميل المحادثة...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700/50 h-[600px] flex flex-col">
      {/* Header */}
      <CardHeader className="border-b border-slate-700/50 pb-4">
        <CardTitle className="text-white text-lg flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
            <MessageCircle className="h-5 w-5 text-white" />
          </div>
          <div>
            <div>الدعم الفني</div>
            <AdminStatus />
          </div>
        </CardTitle>
      </CardHeader>

      {/* Messages Area */}
      <CardContent className="flex-1 overflow-y-auto p-4 space-y-1">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-3 rounded-lg mb-4">
            {error}
          </div>
        )}

        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-slate-500 mx-auto mb-4" />
              <p className="text-slate-400 mb-2">مرحباً بك في الدعم الفني</p>
              <p className="text-slate-500 text-sm">
                ابدأ محادثة جديدة وسنساعدك في أسرع وقت ممكن
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageBubble key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </CardContent>

      {/* Input Area */}
      <div className="border-t border-slate-700/50 p-4">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={messageInput}
              onChange={(e) => {
                setMessageInput(e.target.value)
                handleTyping()
              }}
              onKeyPress={handleKeyPress}
              placeholder="اكتب رسالتك هنا..."
              className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 pr-12"
              maxLength={1000}
              disabled={!isOnline}
            />
            
            {/* File attachment button */}
            <button
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
              onClick={() => {
                // ## TODO: Implement file upload
                console.log('File upload clicked')
              }}
            >
              <Paperclip className="h-4 w-4" />
            </button>
          </div>
          
          <Button
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || !isOnline}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-6"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Character count */}
        <div className="text-xs text-slate-500 mt-2 text-left">
          {messageInput.length}/1000
        </div>
      </div>
    </Card>
  )
}
